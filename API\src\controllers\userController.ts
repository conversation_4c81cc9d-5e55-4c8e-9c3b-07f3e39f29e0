import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";
import { FileService } from "../services/fileService";

interface UpdateProfileRequest {
  full_name?: string;
  mobile_number?: string;
  current_location?: string;
  linkedin_url?: string;
  company?: string;
  job_title?: string;
  course_id?: number;
  batch_year?: number;
  privacy_settings?: {
    show_email?: boolean;
    show_mobile?: boolean;
    show_linkedin?: boolean;
  };
}

// Note: Connection functionality removed in new schema
// These interfaces are kept for potential future implementation

/**
 * Get current user's profile
 */
export const getProfile = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const user = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                id: true,
                course_name: true,
              },
            },
          },
        },
        _count: {
          select: {
            general_posts: true,
            jobs: true,
          },
        },
      },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    res.json({
      user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update current user's profile
 */
export const updateProfile = async (req: Request<{}, {}, UpdateProfileRequest>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const {
      full_name,
      mobile_number,
      current_location,
      linkedin_url,
      company,
      job_title,
      course_id,
      batch_year,
      privacy_settings,
    } = req.body;

    // Update user basic info
    const userUpdateData: any = {};
    if (full_name !== undefined) userUpdateData.full_name = full_name;
    if (mobile_number !== undefined) userUpdateData.mobile_number = mobile_number;

    let updatedUser;
    if (Object.keys(userUpdateData).length > 0) {
      updatedUser = await prisma.user.update({
        where: { id: parseInt(req.user.userId) },
        data: userUpdateData,
      });
    }

    // Update user profile
    const profileUpdateData: any = {};
    if (current_location !== undefined) profileUpdateData.current_location = current_location;
    if (linkedin_url !== undefined) profileUpdateData.linkedin_url = linkedin_url;
    if (company !== undefined) profileUpdateData.company = company;
    if (job_title !== undefined) profileUpdateData.job_title = job_title;
    if (batch_year !== undefined) profileUpdateData.batch_year = batch_year;

    // Validate course_id if provided
    if (course_id !== undefined) {
      if (course_id === null) {
        profileUpdateData.course_id = null;
      } else {
        // Check if course exists and belongs to the same tenant
        const course = await prisma.course.findFirst({
          where: {
            id: course_id,
            tenant_id: req.user.tenant_id,
          },
        });

        if (!course) {
          throw createError("Invalid course ID or course not found in your organization", 400);
        }

        profileUpdateData.course_id = course_id;
      }
    }
    if (privacy_settings !== undefined) {
      // Merge with existing privacy settings
      const existingProfile = await prisma.userProfile.findUnique({
        where: { user_id: parseInt(req.user.userId) },
        select: { privacy_settings: true },
      });

      profileUpdateData.privacy_settings = {
        ...((existingProfile?.privacy_settings as any) || {}),
        ...privacy_settings,
      };
    }

    if (Object.keys(profileUpdateData).length > 0) {
      await prisma.userProfile.upsert({
        where: { user_id: parseInt(req.user.userId) },
        update: profileUpdateData,
        create: {
          user_id: parseInt(req.user.userId),
          tenant_id: req.user.tenant_id,
          ...profileUpdateData,
        },
      });
    }

    // Fetch updated user with profile
    const user = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      include: {
        profile: {
          include: {
            course: {
              select: {
                id: true,
                course_name: true,
              },
            },
          },
        },
      },
    });

    res.json({
      message: "Profile updated successfully",
      user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user directory with search and filtering
 */
export const getUserDirectory = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const search = req.query.search as string;
    const role = req.query.role as UserRole;
    const course = req.query.course as string;
    const batch_year = req.query.batch_year as string;
    const company = req.query.company as string;

    const skip = (page - 1) * limit;

    // Get current user's tenant_id
    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true },
    });

    if (!currentUser) {
      throw createError("User not found", 404);
    }

    // Build where clause - only show users from same tenant
    const where: any = {
      tenant_id: currentUser.tenant_id,
      account_status: UserStatus.APPROVED,
      NOT: {
        id: parseInt(req.user.userId), // Exclude current user
      },
    };

    if (search) {
      where.OR = [
        { full_name: { contains: search, mode: "insensitive" } },
        { profile: { company: { contains: search, mode: "insensitive" } } },
        { profile: { job_title: { contains: search, mode: "insensitive" } } },
        { profile: { current_location: { contains: search, mode: "insensitive" } } },
      ];
    }

    if (role) {
      where.role = role;
    }

    if (course) {
      where.profile = {
        ...where.profile,
        course: {
          course_name: { contains: course, mode: "insensitive" },
        },
      };
    }

    if (batch_year) {
      where.profile = {
        ...where.profile,
        batch_year: parseInt(batch_year),
      };
    }

    if (company) {
      where.profile = {
        ...where.profile,
        company: { contains: company, mode: "insensitive" },
      };
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          profile: {
            include: {
              course: {
                select: {
                  course_name: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: [
          { role: "asc" }, // Alumni first
          { full_name: "asc" },
        ],
      }),
      prisma.user.count({ where }),
    ]);

    // Filter contact information based on privacy settings
    const filteredUsers = users.map((user) => {
      const privacySettings = (user.profile?.privacy_settings as any) || {};
      return {
        ...user,
        email: privacySettings.show_email ? user.email : null,
        mobile_number: privacySettings.show_mobile ? user.mobile_number : null,
        profile: user.profile
          ? {
              ...user.profile,
              linkedin_url: privacySettings.show_linkedin ? user.profile.linkedin_url : null,
            }
          : null,
      };
    });

    res.json({
      users: filteredUsers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user by ID
 */
export const getUserById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    if (!id) {
      throw createError("User ID is required", 400);
    }

    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    // Get current user's tenant_id for security
    const currentUser = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      select: { tenant_id: true },
    });

    if (!currentUser) {
      throw createError("Current user not found", 404);
    }

    const user = await prisma.user.findFirst({
      where: {
        id: parseInt(id),
        tenant_id: currentUser.tenant_id, // Only allow access to users in same tenant
        account_status: UserStatus.APPROVED,
      },
      include: {
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
        _count: {
          select: {
            general_posts: true,
            jobs: true,
          },
        },
      },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    // Filter contact information based on privacy settings
    const privacySettings = (user.profile?.privacy_settings as any) || {};
    const filteredUser = {
      ...user,
      email: privacySettings.show_email ? user.email : null,
      mobile_number: privacySettings.show_mobile ? user.mobile_number : null,
      profile: user.profile
        ? {
            ...user.profile,
            linkedin_url: privacySettings.show_linkedin ? user.profile.linkedin_url : null,
          }
        : null,
    };

    res.json({
      user: filteredUser,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

// Connection functionality removed in new schema
// These methods return 501 Not Implemented for potential future implementation

export const getConnections = async (req: Request, res: Response, next: NextFunction) => {
  // Implementation removed - connections not part of new schema
  throw createError("Connections feature not available", 501);
};

export const sendConnectionRequest = async (req: Request, res: Response, next: NextFunction) => {
  // Implementation removed - connections not part of new schema
  throw createError("Connections feature not available", 501);
};

export const respondToConnection = async (req: Request, res: Response, next: NextFunction) => {
  // Implementation removed - connections not part of new schema
  throw createError("Connections feature not available", 501);
};

export const getConnectionRequests = async (req: Request, res: Response, next: NextFunction) => {
  // Implementation removed - connections not part of new schema
  throw createError("Connections feature not available", 501);
};

/**
 * Get available courses for the user's tenant
 */
export const getCourses = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const courses = await prisma.course.findMany({
      where: {
        tenant_id: req.user.tenant_id,
      },
      select: {
        id: true,
        course_name: true,
      },
      orderBy: {
        course_name: "asc",
      },
    });

    res.json({
      courses,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Upload profile picture
 */
export const uploadProfilePicture = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    if (!req.file) {
      throw createError("No file uploaded", 400);
    }

    // Validate the uploaded file
    const validation = FileService.validateImageFile(req.file);
    if (!validation.isValid) {
      // Delete the uploaded file if validation fails
      FileService.deleteFile(FileService.getRelativePath(req.file.path));
      throw createError(validation.error || "Invalid file", 400);
    }

    // Get the relative path for storing in database
    const relativePath = FileService.getRelativePath(req.file.path);
    const imageUrl = FileService.getFileUrl(relativePath);

    // Get current user profile to delete old profile picture if exists
    const currentProfile = await prisma.userProfile.findUnique({
      where: { user_id: parseInt(req.user.userId) },
      select: { profile_picture_url: true },
    });

    // Update user profile with new profile picture
    const updatedProfile = await prisma.userProfile.upsert({
      where: { user_id: parseInt(req.user.userId) },
      update: {
        profile_picture_url: imageUrl,
        updated_at: new Date(),
      },
      create: {
        user_id: parseInt(req.user.userId),
        tenant_id: req.user.tenant_id,
        profile_picture_url: imageUrl,
      },
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
          },
        },
      },
    });

    // Delete old profile picture if it exists and is different
    if (currentProfile?.profile_picture_url && currentProfile.profile_picture_url !== imageUrl) {
      const oldRelativePath = currentProfile.profile_picture_url.replace("/uploads/", "");
      FileService.deleteFile(oldRelativePath);
    }

    res.json({
      message: "Profile picture uploaded successfully",
      profilePicture: {
        url: imageUrl,
        uploadedAt: new Date().toISOString(),
      },
      user: updatedProfile.user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    // Clean up uploaded file if there's an error
    if (req.file) {
      FileService.deleteFile(FileService.getRelativePath(req.file.path));
    }
    next(error);
  }
};
