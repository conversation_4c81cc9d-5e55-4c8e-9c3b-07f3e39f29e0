const axios = require('axios');

// Test the course validation fix
async function testCourseValidation() {
  const baseURL = 'http://localhost:5000/api';
  
  try {
    // First, let's try to get available courses
    console.log('Testing GET /api/users/courses...');
    
    // You'll need to replace this with a valid JWT token
    const token = 'your-jwt-token-here';
    
    const coursesResponse = await axios.get(`${baseURL}/users/courses`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('Available courses:', coursesResponse.data.courses);
    
    // Test updating profile with valid course_id
    if (coursesResponse.data.courses.length > 0) {
      const validCourseId = coursesResponse.data.courses[0].id;
      
      console.log(`\nTesting profile update with valid course_id: ${validCourseId}`);
      
      const updateResponse = await axios.put(`${baseURL}/users/profile`, {
        course_id: validCourseId,
        batch_year: 2023
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Profile update successful:', updateResponse.data.message);
    }
    
    // Test updating profile with invalid course_id
    console.log('\nTesting profile update with invalid course_id: 99999');
    
    try {
      await axios.put(`${baseURL}/users/profile`, {
        course_id: 99999,
        batch_year: 2023
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.log('Expected error for invalid course_id:', error.response.data.error);
    }
    
    // Test updating profile with null course_id (should work)
    console.log('\nTesting profile update with null course_id...');
    
    const nullCourseResponse = await axios.put(`${baseURL}/users/profile`, {
      course_id: null,
      batch_year: 2023
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Profile update with null course_id successful:', nullCourseResponse.data.message);
    
  } catch (error) {
    if (error.response) {
      console.error('API Error:', error.response.data);
    } else {
      console.error('Network Error:', error.message);
    }
  }
}

// Run the test
console.log('Course Validation Test');
console.log('====================');
console.log('Note: You need to replace "your-jwt-token-here" with a valid JWT token');
console.log('You can get a token by logging in through the /api/auth/login endpoint\n');

// Uncomment the line below and add a valid token to run the test
// testCourseValidation();

console.log('Test script created. To run:');
console.log('1. Get a valid JWT token by logging in');
console.log('2. Replace "your-jwt-token-here" with the actual token');
console.log('3. Uncomment the testCourseValidation() call');
console.log('4. Run: node test-course-validation.js');
